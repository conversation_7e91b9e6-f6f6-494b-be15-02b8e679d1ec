-- Apple Analytics Database Schema Update
-- Add session metrics support to existing apple_analytics_data table

-- First, check if the table exists and add new columns
ALTER TABLE apple_analytics_data 
ADD COLUMN IF NOT EXISTS sessions INTEGER CHECK (sessions >= 0),
ADD COLUMN IF NOT EXISTS total_session_duration INTEGER CHECK (total_session_duration >= 0),
ADD COLUMN IF NOT EXISTS avg_session_duration FLOAT CHECK (avg_session_duration >= 0);

-- Add comments to new columns
COMMENT ON COLUMN apple_analytics_data.sessions IS 'Number of app sessions (from App Sessions reports)';
COMMENT ON COLUMN apple_analytics_data.total_session_duration IS 'Total session duration in seconds (from App Sessions reports)';
COMMENT ON COLUMN apple_analytics_data.avg_session_duration IS 'Average session duration in seconds (calculated field)';

-- Create additional indexes for session data queries
CREATE INDEX IF NOT EXISTS idx_apple_analytics_sessions ON apple_analytics_data (sessions) WHERE sessions IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_apple_analytics_session_duration ON apple_analytics_data (total_session_duration) WHERE total_session_duration IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_apple_analytics_event_sessions ON apple_analytics_data (event_type, sessions) WHERE sessions IS NOT NULL;

-- Create a view for session analytics
CREATE OR REPLACE VIEW apple_session_analytics AS
SELECT 
    report_date,
    app_id,
    app_name,
    app_version,
    device_type,
    platform_version,
    territory,
    
    -- Session metrics
    SUM(sessions) as total_sessions,
    SUM(total_session_duration) as total_duration_seconds,
    SUM(unique_device_count) as total_active_users,
    
    -- Calculated metrics
    CASE 
        WHEN SUM(sessions) > 0 THEN 
            SUM(total_session_duration)::FLOAT / SUM(sessions)
        ELSE 0 
    END as avg_session_duration_seconds,
    
    CASE 
        WHEN SUM(unique_device_count) > 0 THEN 
            SUM(sessions)::FLOAT / SUM(unique_device_count)
        ELSE 0 
    END as sessions_per_user,
    
    -- Convert to human readable formats
    CASE 
        WHEN SUM(sessions) > 0 THEN 
            ROUND(CAST((SUM(total_session_duration)::FLOAT / SUM(sessions)) / 60 AS NUMERIC), 2)
        ELSE 0 
    END as avg_session_duration_minutes,
    
    ROUND(CAST(SUM(total_session_duration)::FLOAT / 3600 AS NUMERIC), 2) as total_duration_hours
    
FROM apple_analytics_data 
WHERE event_type = 'session' 
  AND sessions IS NOT NULL
GROUP BY 
    report_date, app_id, app_name, app_version, 
    device_type, platform_version, territory
ORDER BY report_date DESC;

-- Create a daily summary view combining all metrics
CREATE OR REPLACE VIEW apple_daily_summary AS
WITH session_data AS (
    SELECT 
        report_date,
        SUM(sessions) as daily_sessions,
        SUM(total_session_duration) as daily_duration_seconds,
        SUM(unique_device_count) as daily_active_users,
        CASE 
            WHEN SUM(sessions) > 0 THEN 
                SUM(total_session_duration)::FLOAT / SUM(sessions)
            ELSE 0 
        END as avg_session_duration
    FROM apple_analytics_data 
    WHERE event_type = 'session' AND sessions IS NOT NULL
    GROUP BY report_date
),
install_data AS (
    SELECT 
        report_date,
        SUM(event_count) as daily_installs,
        SUM(unique_device_count) as daily_install_devices
    FROM apple_analytics_data 
    WHERE event_type = 'install'
    GROUP BY report_date
),
uninstall_data AS (
    SELECT 
        report_date,
        SUM(event_count) as daily_uninstalls,
        SUM(unique_device_count) as daily_uninstall_devices
    FROM apple_analytics_data 
    WHERE event_type = 'uninstall'
    GROUP BY report_date
)
SELECT 
    COALESCE(s.report_date, i.report_date, u.report_date) as report_date,
    
    -- Session metrics
    COALESCE(s.daily_sessions, 0) as daily_sessions,
    COALESCE(s.daily_active_users, 0) as daily_active_users,
    COALESCE(s.daily_duration_seconds, 0) as daily_duration_seconds,
    ROUND(CAST(COALESCE(s.daily_duration_seconds, 0)::FLOAT / 3600 AS NUMERIC), 2) as daily_duration_hours,
    ROUND(CAST(COALESCE(s.avg_session_duration, 0) / 60 AS NUMERIC), 2) as avg_session_duration_minutes,
    
    -- Install/Uninstall metrics
    COALESCE(i.daily_installs, 0) as daily_installs,
    COALESCE(i.daily_install_devices, 0) as daily_install_devices,
    COALESCE(u.daily_uninstalls, 0) as daily_uninstalls,
    COALESCE(u.daily_uninstall_devices, 0) as daily_uninstall_devices,
    
    -- Net growth
    COALESCE(i.daily_install_devices, 0) - COALESCE(u.daily_uninstall_devices, 0) as net_user_growth,
    
    -- Engagement metrics
    CASE 
        WHEN COALESCE(s.daily_active_users, 0) > 0 THEN 
            COALESCE(s.daily_sessions, 0)::FLOAT / s.daily_active_users
        ELSE 0 
    END as sessions_per_active_user

FROM session_data s
FULL OUTER JOIN install_data i ON s.report_date = i.report_date  
FULL OUTER JOIN uninstall_data u ON s.report_date = u.report_date
ORDER BY report_date DESC;

-- Grant permissions (adjust role names as needed for your setup)
-- GRANT SELECT ON apple_session_analytics TO authenticated;
-- GRANT SELECT ON apple_daily_summary TO authenticated;

-- The schema is now ready for Apple Analytics with session support!
-- You can now run queries like:
-- SELECT * FROM apple_daily_summary ORDER BY report_date DESC LIMIT 7;
-- SELECT * FROM apple_session_analytics WHERE report_date >= CURRENT_DATE - INTERVAL '30 days';