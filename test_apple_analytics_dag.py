#!/usr/bin/env python3
"""
Test Apple Analytics DAG End-to-End

This script tests the complete Apple Analytics DAG workflow:
1. Load environment variables from .env file
2. Validate Apple API credentials
3. Fetch analytics data from Apple API
4. Transform data to normalized format
5. Upload to Supabase database
6. Verify data integrity

Usage:
    python test_apple_analytics_dag.py

Requirements:
    - .env file with Apple API and Supabase credentials
    - Supabase table 'apple_analytics_data' created
    - Network access to Apple API and Supabase
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

# Add the dags directory to Python path
dags_path = Path(__file__).parent / "dags"
sys.path.insert(0, str(dags_path))

# Load environment variables from .env file
def load_env_file():
    """Load environment variables from .env file."""
    env_file = Path(__file__).parent / ".env"
    
    if not env_file.exists():
        print("❌ .env file not found. Please create one with the required credentials.")
        print("\nRequired variables:")
        print("APPLE_KEY_ID=your_key_id")
        print("APPLE_ISSUER_ID=your_issuer_id")
        print("APPLE_PRIVATE_KEY_PATH=path_to_private_key.p8")
        print("APPLE_APP_ID=your_app_id")
        print("SUPABASE_URL=your_supabase_url")
        print("SUPABASE_SERVICE_ROLE_KEY=your_service_role_key")
        sys.exit(1)
    
    print(f"📁 Loading environment variables from {env_file}")
    
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip().strip('"').strip("'")
    
    print("✅ Environment variables loaded")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def create_mock_context():
    """Create a mock Airflow context for testing."""
    class MockTaskInstance:
        def __init__(self):
            self._xcom_data = {}
        
        def xcom_pull(self, task_ids=None, key=None):
            if task_ids and key:
                return self._xcom_data.get(f"{task_ids}_{key}")
            elif task_ids:
                return self._xcom_data.get(task_ids)
            return None
        
        def xcom_push(self, key=None, value=None, task_id=None):
            if task_id and key:
                self._xcom_data[f"{task_id}_{key}"] = value
            elif key:
                self._xcom_data[key] = value
            else:
                # For task results, use the calling task name
                import inspect
                frame = inspect.currentframe().f_back
                func_name = frame.f_code.co_name
                self._xcom_data[func_name] = value
    
    task_instance = MockTaskInstance()
    
    return {
        "task_instance": task_instance,
        "execution_date": datetime.now(),
        "ds": datetime.now().strftime("%Y-%m-%d"),
        "ts": datetime.now().isoformat(),
    }

def test_credentials_validation():
    """Test Apple API credentials validation."""
    print("\n" + "="*60)
    print("🔐 TESTING CREDENTIALS VALIDATION")
    print("="*60)
    
    try:
        from dependencies.apple.apple_tasks import validate_apple_credentials
        
        context = create_mock_context()
        result = validate_apple_credentials(**context)
        
        if result["success"]:
            print(f"✅ Credentials validation passed")
            print(f"📊 Available reports: {result['available_reports']}")
            return True
        else:
            print(f"❌ Credentials validation failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Credentials validation exception: {e}")
        return False

def test_data_fetch():
    """Test Apple Analytics data fetching."""
    print("\n" + "="*60)
    print("📥 TESTING DATA FETCH")
    print("="*60)
    
    try:
        from dependencies.apple.apple_tasks import fetch_apple_analytics_data
        
        context = create_mock_context()
        result = fetch_apple_analytics_data(**context)
        
        if result["success"]:
            print(f"✅ Data fetch successful")
            print(f"📊 Reports fetched: {result['successful_fetches']}/{result['total_enabled']}")
            
            # Store result in mock context for next test
            context["task_instance"].xcom_push("fetch_apple_analytics_data", result)
            
            # Show summary of fetched data
            for report_name, data in result["fetched_data"].items():
                csv_lines = data["csv_content"].count('\n')
                print(f"  📄 {report_name}: {csv_lines} lines")
            
            return True, context
        else:
            print(f"❌ Data fetch failed: {result.get('error', 'Unknown error')}")
            return False, None
            
    except Exception as e:
        print(f"❌ Data fetch exception: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_data_transformation(context):
    """Test data transformation."""
    print("\n" + "="*60)
    print("🔄 TESTING DATA TRANSFORMATION")
    print("="*60)
    
    try:
        from dependencies.apple.apple_tasks import transform_apple_analytics_data
        
        result = transform_apple_analytics_data(**context)
        
        if result["success"]:
            print(f"✅ Data transformation successful")
            print(f"📊 Total records: {result['total_records']}")
            print(f"📋 Reports processed: {result['successful_transforms']}/{result['total_reports']}")
            
            # Store result in context for next test
            context["task_instance"].xcom_push("transform_apple_analytics_data", result)
            
            # Show transformation stats
            for report_name, stats in result["transformation_stats"].items():
                if "error" not in stats:
                    print(f"  📄 {report_name}: {stats['total_records']} records")
                else:
                    print(f"  ❌ {report_name}: {stats['error']}")
            
            return True
        else:
            print(f"❌ Data transformation failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Data transformation exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_supabase_upload(context):
    """Test Supabase upload."""
    print("\n" + "="*60)
    print("📤 TESTING SUPABASE UPLOAD")
    print("="*60)
    
    try:
        from dependencies.apple.apple_tasks import upload_to_supabase
        
        result = upload_to_supabase(**context)
        
        if result["success"]:
            print(f"✅ Supabase upload successful")
            print(f"📊 Records uploaded: {result['uploaded_count']}/{result['total_records']}")
            
            if result["error_count"] > 0:
                print(f"⚠️ Errors encountered: {result['error_count']}")
                for error in result["errors"]:
                    print(f"  ❌ {error}")
            
            return True
        else:
            print(f"❌ Supabase upload failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Supabase upload exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_integrity():
    """Test data integrity in Supabase."""
    print("\n" + "="*60)
    print("🔍 TESTING DATA INTEGRITY")
    print("="*60)
    
    try:
        from supabase import create_client
        
        # Get credentials
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        if not supabase_url or not supabase_key:
            print("❌ Supabase credentials not found")
            return False
        
        # Connect to Supabase
        supabase = create_client(supabase_url, supabase_key)
        
        # Query recent data
        today = datetime.now().strftime("%Y-%m-%d")
        
        # Test basic query
        result = supabase.table("apple_analytics_data")\
            .select("count", count="exact")\
            .execute()
        
        total_count = result.count if hasattr(result, 'count') else 0
        print(f"📊 Total records in database: {total_count}")
        
        # Test recent data
        result = supabase.table("apple_analytics_data")\
            .select("*")\
            .order("processed_at", desc=True)\
            .limit(5)\
            .execute()
        
        recent_records = result.data if result.data else []
        print(f"📅 Recent records: {len(recent_records)}")
        
        if recent_records:
            print("Sample recent records:")
            for i, record in enumerate(recent_records[:3]):
                print(f"  {i+1}. {record['event_type']} - {record['device_type']} - {record['territory']} ({record['event_count']} events)")
        
        # Test data quality
        result = supabase.table("apple_analytics_data")\
            .select("event_type", count="exact")\
            .execute()
        
        # Group by event type
        if result.data:
            event_types = {}
            for record in result.data:
                event_type = record['event_type']
                event_types[event_type] = event_types.get(event_type, 0) + 1
            
            print(f"📈 Event types distribution:")
            for event_type, count in event_types.items():
                print(f"  {event_type}: {count} records")
        
        return True
        
    except Exception as e:
        print(f"❌ Data integrity test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_database_schema():
    """Show the required database schema."""
    print("\n" + "="*60)
    print("🗄️ REQUIRED DATABASE SCHEMA")
    print("="*60)
    
    schema_sql = '''
-- Create the main analytics table with session support
CREATE TABLE apple_analytics_data (
    unique_id VARCHAR(32) PRIMARY KEY,
    app_id VARCHAR(20) NOT NULL,
    report_date DATE NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_subtype VARCHAR(50),
    app_name VARCHAR(255) NOT NULL,
    app_version VARCHAR(50) NOT NULL,
    device_type VARCHAR(50) NOT NULL,
    platform_version VARCHAR(50) NOT NULL,
    source_type VARCHAR(100) NOT NULL,
    source_app VARCHAR(255),
    campaign_name VARCHAR(255),
    page_type VARCHAR(100),
    page_title VARCHAR(255),
    territory VARCHAR(10) NOT NULL,
    event_count INTEGER NOT NULL CHECK (event_count >= 0),
    unique_device_count INTEGER NOT NULL CHECK (unique_device_count >= 0),
    sessions INTEGER CHECK (sessions >= 0),
    total_session_duration INTEGER CHECK (total_session_duration >= 0),
    avg_session_duration FLOAT CHECK (avg_session_duration >= 0),
    download_date DATE,
    processed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    data_source VARCHAR(100) NOT NULL DEFAULT 'apple_analytics'
);

-- Create indexes for performance
CREATE INDEX idx_apple_analytics_app_date ON apple_analytics_data (app_id, report_date);
CREATE INDEX idx_apple_analytics_event_type ON apple_analytics_data (event_type);
CREATE INDEX idx_apple_analytics_territory ON apple_analytics_data (territory);
CREATE INDEX idx_apple_analytics_device ON apple_analytics_data (device_type);
CREATE INDEX idx_apple_analytics_source ON apple_analytics_data (source_type);
CREATE INDEX idx_apple_analytics_processed ON apple_analytics_data (processed_at);
CREATE INDEX idx_apple_analytics_sessions ON apple_analytics_data (sessions) WHERE sessions IS NOT NULL;
'''
    
    print(schema_sql)
    print("\n📋 Please run this SQL in your Supabase SQL editor before running the test.")

def main():
    """Run the complete test suite."""
    print("🍎 Apple Analytics DAG End-to-End Test")
    print("=" * 60)
    
    # Load environment variables
    load_env_file()
    
    # Check required environment variables
    required_vars = [
        "APPLE_KEY_ID", "APPLE_ISSUER_ID", "APPLE_PRIVATE_KEY_PATH", "APPLE_APP_ID",
        "SUPABASE_URL", "SUPABASE_SERVICE_ROLE_KEY"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ All required environment variables found")
    
    # Show database schema requirements
    show_database_schema()
    
    # Assume database is ready (skip interactive confirmation)
    print("\n🚀 Proceeding with tests (assuming Supabase table exists)...")
    
    # Run test suite
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Credentials validation
    if test_credentials_validation():
        tests_passed += 1
    
    # Test 2: Data fetch
    fetch_success, context = test_data_fetch()
    if fetch_success:
        tests_passed += 1
        
        # Test 3: Data transformation
        if test_data_transformation(context):
            tests_passed += 1
            
            # Test 4: Supabase upload
            if test_supabase_upload(context):
                tests_passed += 1
                
                # Test 5: Data integrity
                if test_data_integrity():
                    tests_passed += 1
    
    # Final results
    print("\n" + "="*60)
    print("🏁 TEST RESULTS")
    print("="*60)
    
    if tests_passed == total_tests:
        print(f"🎉 ALL TESTS PASSED! ({tests_passed}/{total_tests})")
        print("✅ The Apple Analytics DAG is ready for production!")
        return True
    else:
        print(f"❌ SOME TESTS FAILED ({tests_passed}/{total_tests})")
        print("Please fix the issues and run the test again.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)